import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

// Types pour les couleurs du cycle jour/nuit
interface TimeColor {
  hour: number;
  colors: {
    primary: string;
    secondary: string;
    tertiary: string;
  };
}

// Palette de couleurs pour chaque moment de la journée
// Format: { primary: bas (horizon), secondary: milieu, tertiary: haut (ciel) }
const TIME_COLORS: TimeColor[] = [
  // Nuit profonde (0h-4h) - Très sombre
  { hour: 0, colors: { primary: '#1a1a2e', secondary: '#16213e', tertiary: '#0f0f23' } },
  { hour: 1, colors: { primary: '#1a1a2e', secondary: '#16213e', tertiary: '#0f0f23' } },
  { hour: 2, colors: { primary: '#1a1a2e', secondary: '#16213e', tertiary: '#0f0f23' } },
  { hour: 3, colors: { primary: '#1a1a2e', secondary: '#16213e', tertiary: '#0f0f23' } },
  { hour: 4, colors: { primary: '#1a1a2e', secondary: '#16213e', tertiary: '#0f0f23' } },

  // Aube (5h-7h) - Transition douce
  { hour: 5, colors: { primary: '#2d1b69', secondary: '#1a1a3a', tertiary: '#0f0f23' } },
  { hour: 6, colors: { primary: '#ff6b9d', secondary: '#4a2c7a', tertiary: '#1a1a3a' } },
  { hour: 7, colors: { primary: '#ffab91', secondary: '#ff8a80', tertiary: '#4a2c7a' } },

  // Matin (8h-11h) - Lumineux progressif
  { hour: 8, colors: { primary: '#fff3e0', secondary: '#ffcc80', tertiary: '#81d4fa' } },
  { hour: 9, colors: { primary: '#e1f5fe', secondary: '#b3e5fc', tertiary: '#4fc3f7' } },
  { hour: 10, colors: { primary: '#b3e5fc', secondary: '#81d4fa', tertiary: '#29b6f6' } },
  { hour: 11, colors: { primary: '#81d4fa', secondary: '#4fc3f7', tertiary: '#03a9f4' } },

  // Midi (12h-14h) - Maximum luminosité
  { hour: 12, colors: { primary: '#e1f5fe', secondary: '#03a9f4', tertiary: '#0277bd' } },
  { hour: 13, colors: { primary: '#e1f5fe', secondary: '#0288d1', tertiary: '#01579b' } },
  { hour: 14, colors: { primary: '#b3e5fc', secondary: '#0277bd', tertiary: '#01579b' } },

  // Après-midi (15h-17h) - Transition vers le chaud
  { hour: 15, colors: { primary: '#81d4fa', secondary: '#29b6f6', tertiary: '#0277bd' } },
  { hour: 16, colors: { primary: '#ffcc80', secondary: '#4fc3f7', tertiary: '#0288d1' } },
  { hour: 17, colors: { primary: '#ffab91', secondary: '#81d4fa', tertiary: '#29b6f6' } },

  // Crépuscule (18h-20h) - Coucher de soleil
  { hour: 18, colors: { primary: '#ff8a65', secondary: '#ff7043', tertiary: '#4a148c' } },
  { hour: 19, colors: { primary: '#ff5722', secondary: '#e64a19', tertiary: '#2d1b69' } },
  { hour: 20, colors: { primary: '#d84315', secondary: '#bf360c', tertiary: '#1a1a3a' } },

  // Soirée (21h-23h) - Retour vers la nuit
  { hour: 21, colors: { primary: '#8e24aa', secondary: '#7b1fa2', tertiary: '#0f0f23' } },
  { hour: 22, colors: { primary: '#6a1b9a', secondary: '#4a148c', tertiary: '#0f0f23' } },
  { hour: 23, colors: { primary: '#4a148c', secondary: '#2d1b69', tertiary: '#0f0f23' } },
];

// Interface pour les props du composant
interface DynamicBackgroundProps {
  children: React.ReactNode;
  simulatedHour?: number; // Pour les tests
}

const DynamicBackground: React.FC<DynamicBackgroundProps> = ({ children, simulatedHour }) => {
  const backgroundRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<gsap.core.Timeline | null>(null);
  const lastUpdateRef = useRef<string>('');
  const animationFrameRef = useRef<number | null>(null);

  // Fonction pour interpoler entre deux couleurs
  const interpolateColor = (color1: string, color2: string, factor: number): string => {
    const hex1 = color1.replace('#', '');
    const hex2 = color2.replace('#', '');
    
    const r1 = parseInt(hex1.substr(0, 2), 16);
    const g1 = parseInt(hex1.substr(2, 2), 16);
    const b1 = parseInt(hex1.substr(4, 2), 16);
    
    const r2 = parseInt(hex2.substr(0, 2), 16);
    const g2 = parseInt(hex2.substr(2, 2), 16);
    const b2 = parseInt(hex2.substr(4, 2), 16);
    
    const r = Math.round(r1 + (r2 - r1) * factor);
    const g = Math.round(g1 + (g2 - g1) * factor);
    const b = Math.round(b1 + (b2 - b1) * factor);
    
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  };

  // Fonction pour obtenir les couleurs actuelles basées sur l'heure
  const getCurrentColors = () => {
    const now = new Date();
    const currentHour = simulatedHour !== undefined ? simulatedHour : now.getHours();
    const currentMinute = now.getMinutes();
    const currentSecond = now.getSeconds();
    
    // Calcul du facteur de progression dans l'heure (0-1)
    const hourProgress = (currentMinute * 60 + currentSecond) / 3600;
    
    // Trouver les couleurs de l'heure actuelle et de la suivante
    const currentTimeColor = TIME_COLORS.find(tc => tc.hour === currentHour) || TIME_COLORS[0];
    const nextHour = (currentHour + 1) % 24;
    const nextTimeColor = TIME_COLORS.find(tc => tc.hour === nextHour) || TIME_COLORS[0];
    
    // Interpoler entre les couleurs actuelles et suivantes
    const primary = interpolateColor(currentTimeColor.colors.primary, nextTimeColor.colors.primary, hourProgress);
    const secondary = interpolateColor(currentTimeColor.colors.secondary, nextTimeColor.colors.secondary, hourProgress);
    const tertiary = interpolateColor(currentTimeColor.colors.tertiary, nextTimeColor.colors.tertiary, hourProgress);
    
    return { primary, secondary, tertiary };
  };

  // Fonction pour mettre à jour l'arrière-plan avec optimisations
  const updateBackground = () => {
    if (!backgroundRef.current) return;

    const colors = getCurrentColors();
    const gradient = `linear-gradient(to top, ${colors.primary} 0%, ${colors.secondary} 50%, ${colors.tertiary} 100%)`;

    // Éviter les mises à jour inutiles si les couleurs n'ont pas changé
    if (lastUpdateRef.current === gradient) return;
    lastUpdateRef.current = gradient;

    // Annuler l'animation précédente pour éviter les conflits
    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    // Animation fluide avec GSAP optimisée
    timelineRef.current = gsap.timeline();
    timelineRef.current.to(backgroundRef.current, {
      background: gradient,
      duration: 0.3,
      ease: "power1.out",
      force3D: true, // Utiliser l'accélération GPU
      willChange: "background"
    });
  };

  // Fonction optimisée pour les mises à jour en temps réel
  const scheduleUpdate = () => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }

    animationFrameRef.current = requestAnimationFrame(() => {
      updateBackground();
      // Programmer la prochaine mise à jour
      setTimeout(scheduleUpdate, 1000);
    });
  };

  useEffect(() => {
    // Mise à jour initiale
    updateBackground();

    // Démarrer le cycle de mise à jour optimisé
    scheduleUpdate();

    // Nettoyage complet
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (timelineRef.current) {
        timelineRef.current.kill();
      }
    };
  }, []);

  return (
    <div 
      ref={backgroundRef}
      className="min-h-screen transition-all duration-500 ease-out"
      style={{
        background: 'linear-gradient(to top, #1a1a2e 0%, #16213e 50%, #0f0f23 100%)'
      }}
    >
      {children}
    </div>
  );
};

export default DynamicBackground;
