import React, { useState, useEffect } from 'react';
import * as SunCalc from 'suncalc';

// Interface pour les props
interface AstronomyDebuggerProps {
  onSimulatedHourChange: (hour: number | undefined) => void;
}

// Composant de débogage pour tester le système astronomique
const AstronomyDebugger: React.FC<AstronomyDebuggerProps> = ({ onSimulatedHourChange }) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [simulatedHour, setSimulatedHour] = useState<number | null>(null);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getMoonPhaseInfo = () => {
    const moonIllumination = SunCalc.getMoonIllumination(currentTime);
    const fraction = moonIllumination.fraction;
    
    let phaseName = '';
    if (fraction < 0.1) phaseName = '🌑 Nouvelle lune';
    else if (fraction < 0.3) phaseName = '🌒 Premier croissant';
    else if (fraction < 0.7) phaseName = '🌓 Premier quartier';
    else if (fraction < 0.9) phaseName = '🌔 Gibbeuse croissante';
    else phaseName = '🌕 Pleine lune';
    
    return { fraction: (fraction * 100).toFixed(1), phaseName };
  };

  const getStarsVisibility = (hour: number) => {
    if (hour >= 7 && hour < 19) return '0% (Jour)';
    if (hour >= 19 && hour < 20) return `${((hour - 19) * 30).toFixed(0)}% (Apparition)`;
    if (hour >= 20 && hour < 21) return `${(30 + (hour - 20) * 70).toFixed(0)}% (Apparition)`;
    if (hour >= 21 || hour < 5) return '100% (Nuit complète)';
    if (hour >= 5 && hour < 6) return `${(100 - (hour - 5) * 70).toFixed(0)}% (Disparition)`;
    if (hour >= 6 && hour < 7) return `${(30 - (hour - 6) * 30).toFixed(0)}% (Disparition)`;
    return '0%';
  };

  const currentHour = simulatedHour !== null ? simulatedHour : currentTime.getHours();
  const moonInfo = getMoonPhaseInfo();

  return (
    <div className="fixed top-4 left-4 bg-black/90 text-white p-4 rounded-lg backdrop-blur-sm z-50 min-w-[280px]">
      <h3 className="text-lg font-bold mb-2">🌌 Débogueur Astronomique</h3>
      
      <div className="space-y-2 text-sm">
        <div>
          <strong>Heure réelle:</strong> {formatTime(currentTime)}
        </div>
        
        <div>
          <strong>Heure testée:</strong> {currentHour}h
        </div>
        
        <div className="border-t border-gray-600 pt-2">
          <strong>🌙 Lune:</strong>
          <div className="ml-2 text-xs">
            <div>{moonInfo.phaseName}</div>
            <div>Illumination: {moonInfo.fraction}%</div>
          </div>
        </div>
        
        <div>
          <strong>✨ Étoiles:</strong> {getStarsVisibility(currentHour)}
        </div>
        
        <div className="border-t border-gray-600 pt-2 mt-3">
          <strong>Test rapide:</strong>
          <div className="grid grid-cols-4 gap-1 mt-2">
            {[6, 12, 18, 22].map(hour => (
              <button
                key={hour}
                onClick={() => {
                  setSimulatedHour(hour);
                  onSimulatedHourChange(hour);
                }}
                className={`px-2 py-1 text-xs rounded ${
                  simulatedHour === hour
                    ? 'bg-teal-600 text-white'
                    : 'bg-gray-700 hover:bg-gray-600'
                }`}
              >
                {hour}h
              </button>
            ))}
          </div>

          <button
            onClick={() => {
              setSimulatedHour(null);
              onSimulatedHourChange(undefined);
            }}
            className="w-full mt-2 px-2 py-1 text-xs bg-red-600 hover:bg-red-700 rounded"
          >
            Temps réel
          </button>
        </div>
      </div>
    </div>
  );
};

export default AstronomyDebugger;
